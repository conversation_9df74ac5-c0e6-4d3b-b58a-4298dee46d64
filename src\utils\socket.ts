import { io, Socket } from 'socket.io-client'

const SOCKET_URL = import.meta.env.VITE_SOCKET_URL || 'http://localhost:3001'

class SocketManager {
  private socket: Socket | null = null
  private token: string | null = null
  private isAuthenticated: boolean = false
  private authenticationPromise: Promise<void> | null = null

  connect(token: string): Socket {
    if (this.socket?.connected) {
      this.socket.disconnect()
    }

    this.token = token
    this.isAuthenticated = false
    this.socket = io(SOCKET_URL, {
      autoConnect: false
    })

    // Connect and authenticate
    this.socket.connect()

    // Create authentication promise
    this.authenticationPromise = new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Authentication timeout'))
      }, 10000) // 10 second timeout

      this.socket!.once('authenticated', () => {
        clearTimeout(timeout)
        this.isAuthenticated = true
        console.log('Socket authenticated successfully')
        resolve()
      })

      this.socket!.once('authenticationFailed', (data: { message: string }) => {
        clearTimeout(timeout)
        this.isAuthenticated = false
        console.error('Socket authentication failed:', data.message)
        reject(new Error(data.message))
      })
    })

    this.socket.emit('authenticate', token)

    return this.socket
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
    this.isAuthenticated = false
    this.authenticationPromise = null
  }

  getSocket(): Socket | null {
    return this.socket
  }

  isConnected(): boolean {
    return this.socket?.connected || false
  }

  isSocketAuthenticated(): boolean {
    return this.isAuthenticated
  }

  async waitForAuthentication(): Promise<void> {
    if (this.isAuthenticated) {
      return Promise.resolve()
    }
    if (this.authenticationPromise) {
      return this.authenticationPromise
    }
    throw new Error('No authentication in progress')
  }

  // Convenience methods for common socket operations
  async joinRoom(roomId: string): Promise<void> {
    if (!this.socket) {
      throw new Error('Socket not connected')
    }

    // Wait for authentication before joining room
    await this.waitForAuthentication()

    this.socket.emit('joinRoom', roomId)
  }

  leaveRoom(roomId: string): void {
    if (this.socket) {
      this.socket.emit('leaveRoom', roomId)
    }
  }

  sendMessage(content: string, roomId: string): void {
    if (this.socket) {
      this.socket.emit('sendMessage', { content, roomId })
    }
  }

  startTyping(roomId: string): void {
    if (this.socket) {
      this.socket.emit('typingStart', { roomId })
    }
  }

  stopTyping(roomId: string): void {
    if (this.socket) {
      this.socket.emit('typingStop', { roomId })
    }
  }

  // Event listener helpers
  onAuthenticated(callback: (data: { user: unknown }) => void): void {
    this.socket?.on('authenticated', callback)
  }

  onAuthenticationFailed(callback: (data: { message: string }) => void): void {
    this.socket?.on('authenticationFailed', callback)
  }

  onNewMessage(callback: (message: unknown) => void): void {
    this.socket?.on('newMessage', callback)
  }

  onUserJoined(callback: (data: unknown) => void): void {
    this.socket?.on('userJoined', callback)
  }

  onUserLeft(callback: (data: unknown) => void): void {
    this.socket?.on('userLeft', callback)
  }

  onOnlineUsersUpdate(callback: (data: unknown) => void): void {
    this.socket?.on('onlineUsersUpdate', callback)
  }

  onJoinedRoom(callback: (data: { roomId: string; roomName?: string }) => void): void {
    this.socket?.on('joinedRoom', callback)
  }

  onError(callback: (data: { message: string }) => void): void {
    this.socket?.on('error', callback)
  }

  onConnect(callback: () => void): void {
    this.socket?.on('connect', callback)
  }

  onDisconnect(callback: () => void): void {
    this.socket?.on('disconnect', callback)
  }

  onUserTyping(
    callback: (data: { userId: string; username: string; roomId: string; isTyping: boolean }) => void
  ): void {
    this.socket?.on('userTyping', callback)
  }

  // Remove event listeners
  off(event: string, callback?: (...args: unknown[]) => void): void {
    this.socket?.off(event, callback)
  }
}

// Export singleton instance
export const socketManager = new SocketManager()
export default socketManager
