import { useReducer, useEffect, ReactNode } from 'react'
import { LoginRequest, RegisterRequest } from '../types'
import { authApi, ApiError } from '../utils/api'
import socketManager from '../utils/socket'
import { AuthContext, AuthContextType, initialState, authReducer } from './auth-context'

export function AuthProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(authReducer, initialState)

  // Check for existing token on mount
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        const token = localStorage.getItem('token')

        if (token) {
          // Set the token in state first
          dispatch({
            type: 'SET_INITIAL_TOKEN',
            payload: token
          })

          // Verify token is still valid
          const response = await authApi.getMe()

          dispatch({
            type: 'AUTH_SUCCESS',
            payload: { user: response.user, token }
          })

          // Connect to socket if user is authenticated
          socketManager.connect(token)
        } else {
          // No token found, stop loading immediately
          dispatch({ type: 'TOKEN_VALIDATION_COMPLETE' })
        }
      } catch (error) {
        console.error('Token validation error:', error)
        // Token is invalid, remove it
        localStorage.removeItem('token')
        dispatch({ type: 'AUTH_LOGOUT' })
      }
    }

    checkAuthStatus()
  }, [])

  const login = async (data: LoginRequest) => {
    try {
      dispatch({ type: 'AUTH_START' })
      const response = await authApi.login(data)

      localStorage.setItem('token', response.token)
      dispatch({
        type: 'AUTH_SUCCESS',
        payload: { user: response.user, token: response.token }
      })

      // Connect to socket
      socketManager.connect(response.token)
    } catch (error) {
      const message = error instanceof ApiError ? error.message : 'Login failed'
      dispatch({ type: 'AUTH_ERROR', payload: message })
      throw error
    }
  }

  const register = async (data: RegisterRequest) => {
    try {
      dispatch({ type: 'AUTH_START' })
      const response = await authApi.register(data)

      localStorage.setItem('token', response.token)
      dispatch({
        type: 'AUTH_SUCCESS',
        payload: { user: response.user, token: response.token }
      })

      // Connect to socket
      socketManager.connect(response.token)
    } catch (error) {
      const message = error instanceof ApiError ? error.message : 'Registration failed'
      dispatch({ type: 'AUTH_ERROR', payload: message })
      throw error
    }
  }

  const logout = () => {
    localStorage.removeItem('token')
    socketManager.disconnect()
    dispatch({ type: 'AUTH_LOGOUT' })
  }

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' })
  }

  const value: AuthContextType = {
    ...state,
    login,
    register,
    logout,
    clearError
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
